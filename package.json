{"name": "game", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "build-only:test": "vite build --mode test", "build-only:pre": "vite build --mode pre", "type-check": "vue-tsc --build"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@tailwindcss/vite": "^4.1.7", "animejs": "^4.0.2", "axios": "^1.9.0", "crypto-js": "^4.2.0", "gsap": "^3.13.0", "js-md5": "^0.8.3", "mitt": "^3.0.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "protobufjs": "^7.5.3", "radash": "^12.1.0", "swiper": "^11.2.7", "tailwindcss": "^4.1.7", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/crypto-js": "^4.2.2", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "sass-embedded": "^1.89.0", "terser": "^5.42.0", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}