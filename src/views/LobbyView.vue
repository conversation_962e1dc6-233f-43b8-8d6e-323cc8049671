<script setup lang="ts">
import {useDisableZoom} from "@/composables/useDisableZoom.ts";

import CanvasLayout from "@/layout/CanvasLayout.vue";
import LayoutHeader from "@/components/LayoutHeader/index.vue";
import LayoutBody from "@/components/LayoutBody/index.vue";

useDisableZoom();
</script>

<template>
  <CanvasLayout>
    <div id="view-box" class="pt-46 h-screen">
      <div class="fixed inset-x-0 top-0 z-10">
        <LayoutHeader />
      </div>
      <div class="h-full">
        <LayoutBody />
      </div>
    </div>
  </CanvasLayout>
</template>

<style scoped>
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  overflow: hidden;
}
</style>