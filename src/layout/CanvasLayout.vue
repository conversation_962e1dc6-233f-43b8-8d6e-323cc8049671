<script setup lang="ts">
import { useCanvasLayout } from "@/composables/useCanvasLayout.ts";

/**
 * css断点:
 * - 默认(移动端小屏幕)
 * - md(移动端大屏幕)
 * - lg(pc端)
 * 
 * fontsize断点:
 * - 1024 大于等于1024时，固定font-size为10px
 * - 768 大于等于768时，固定font-size为10px，小于768时(缩放)，最小font-size为5px
 */

useCanvasLayout([
  { designDraftWidth: 1024, standardSize: 10, min: 1, max: 1 },
  { designDraftWidth: 768, standardSize: 10, min: 0.5, max: 1},
]);
</script>

<template>
  <div class="view-wrapper">
    <slot />
  </div>
</template>

<style>
.view-wrapper {
  user-select: none;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
