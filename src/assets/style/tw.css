@import "tailwindcss";

@theme {
    --spacing: 0.1rem; /** default spacing */

    --text-default: 1rem; /** default font size */

    /** 自定义颜色 */
    --color-odds-1: #39A7E9;
    --color-odds-2: #8A3EE7;
    --color-odds-3: #A51D99;

    /** 把tw默认的字号大小修正回原来的大小 */
    --text-xs: 1.2rem;
    --text-sm: 1.4rem;
    --text-base: 1.6rem;
    --text-lg: 1.8rem;
    --text-xl: 2rem;
    --text-2xl: 2.4rem;
    --text-3xl: 3rem;
    --text-4xl: 3.6rem;
    --text-5xl: 4.8rem;
    --text-6xl: 6rem;
    --text-7xl: 7.2rem;
    --text-8xl: 9.6rem;
    --text-9xl: 12.8rem;


    /** 把tw默认的断点大小修正回原来的大小 */
    /* --breakpoint-sm: 64rem; */
    /* --breakpoint-md: 76.8rem; */
    /* --breakpoint-lg: 102.4rem; */
    /* --breakpoint-xl: 128rem; */
    /* --breakpoint-2xl: 153.6rem; */

    /** 把tw默认的圆角大小修正回原来的大小 */
    /* --radius-xs: 0.125rem; */
    /* --radius-sm: 0.25rem; */
    /* --radius-md: 0.375rem; */
    /* --radius-lg: 0.5rem; */
    /* --radius-xl: 0.75rem; */
    /* --radius-2xl: 1rem; */
    /* --radius-3xl: 1.5rem; */
    /* --radius-4xl: 2rem; */


    /** 动画 */
    @keyframes game-bg-run {
        to {
            transform: rotate(360deg);
        }
    }
    --animate-game-bg-run: game-bg-run 12s linear infinite;
}

@utility scrollbar-hidden {
    scrollbar-width: none;
    &::-webkit-scrollbar {
        opacity: 0;
        display: none;
    }
}

@custom-variant active (&:where(.is-active));

@layer components {
    .tab-item {
        @apply flex-1 flex items-center justify-center h-28 rounded-[2.8rem] cursor-pointer outline-0 text-white text-xs font-bold;
        @variant active {
            background-color: #2c2d30;
        }
        @variant hover {
            color: #E21A3C;
        }
    }
}
