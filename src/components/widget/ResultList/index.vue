<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import CapsuleButton from './CapsuleButton.vue';
</script>

<template>
  <Popover class="relative size-full">
    <div class="grid grid-cols-[minmax(0,1fr)_auto]">
      <ul class="flex items-center gap-6 text-sm size-full overflow-hidden">
        <li v-for="i in 35" class="inline-flex items-center justify-center cursor-pointer h-full px-6 rounded-2xl bg-black/80 font-bold text-[#39A7E9]">1.40X</li>
      </ul>

      <PopoverButton>
        <CapsuleButton />
      </PopoverButton>
    </div>

    <transition
      enter-active-class="opacity duration-200 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="opacity duration-150 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <PopoverPanel v-slot="{ close }" class="absolute inset-x-0 z-10 -top-5 w-full min-h-210 overflow-hidden bg-[#26282f] rounded-[1.8rem]">
        <div class="h-36 bg-[#1F2128] flex items-center justify-between">
          <div class="text-base text-white pl-10">Historia de la Ronda</div>
          <CapsuleButton open @click="close()" />
        </div>
        <div class="py-10 px-6 flex gap-x-6 gap-y-8 flex-wrap">
          <span
            v-for="i in 60"
            :class="{
              'text-odds-1': true,
              'text-odds-2': i % 2 === 0,
              'text-odds-3': i % 3 === 0,
            }"
            class="inline-flex items-center cursor-pointer rounded-[1.2rem] h-24 bg-black/80 px-8 text-sm font-bold"
          >
            218.40X
          </span>
        </div>
      </PopoverPanel>
    </transition>
  </Popover>
</template>

<style scoped lang="scss">

</style>