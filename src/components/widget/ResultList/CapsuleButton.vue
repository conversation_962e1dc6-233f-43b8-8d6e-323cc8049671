<script setup lang="ts">
defineProps<{ open?: boolean }>()
</script>

<template>
  <div
    :class="{
      'text-[#767B85]': !open,
      'bg-[#252528] text-[#E21A3C]': open,
    }"
    class="flex items-center justify-center gap-8 w-54 h-26 cursor-pointer rounded-[1.3rem] border border-[#343439]"
  >
    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.438 5.83964e-05C6.25408 -0.00491559 5.09052 0.307945 4.06873 0.905992C3.04695 1.50404 2.2044 2.36535 1.629 3.40006L0 1.77306V6.37706H4.604L2.621 4.39206C3.541 2.62206 5.313 1.41806 7.437 1.41806C10.342 1.41806 12.75 3.82506 12.75 6.73006C12.75 9.63406 10.342 12.0431 7.437 12.0431C5.1 12.0431 3.187 10.5551 2.408 8.50106H0.921C1.7 11.3341 4.321 13.4591 7.438 13.4591C11.192 13.4591 14.167 10.4131 14.167 6.73006C14.167 3.04706 11.121 5.83964e-05 7.437 5.83964e-05H7.438ZM6.375 3.54306V7.15606L9.705 9.13906L10.271 8.21906L7.437 6.51906V3.54106H6.375V3.54306Z" fill="currentColor" />
    </svg>
    <svg :class="open ? 'rotate-180' : 'rotate-0'" width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3534 4.28748L7.96639 8.11713C7.01619 9.19165 5.36533 9.30048 4.27938 8.36039C4.19205 8.28479 4.10991 8.20353 4.0335 8.11713L0.646597 4.28748C-0.303734 3.21296 -0.193728 1.57979 0.892345 0.639567C1.36867 0.227247 1.98007 -1.37923e-05 2.61297 6.27809e-10H9.38705C10.8301 6.27809e-10 12 1.15737 12 2.58515C11.9999 3.21136 11.7703 3.81626 11.3534 4.28748Z" fill="currentColor" />
    </svg>
  </div>
</template>

<style scoped lang="scss">

</style>