<script setup lang="ts">
import { ref } from "vue";

import { Tab, TabGroup, TabList } from "@headlessui/vue";
import GInputNumber from "@/components/ui/GInputNumber.vue"

const tabs = ref([ 'Bet', 'Auto' ]);
const bet = ref(5);
</script>

<template>
  <div class="rounded-2xl bg-[#1B1C1D]">
    <div class="p-12 flex flex-col items-center">
      <TabGroup>
        <div class="p-2 bg-[#141516] rounded-3xl">
          <TabList class="flex">
            <Tab v-for="tab of tabs" v-slot="{ selected }" as="template" class="w-117">
              <div :class="{ 'is-active': selected }" class="tab-item">
                {{ tab }}
              </div>
            </Tab>
          </TabList>
        </div>
        <GInputNumber v-model="bet" center class="font-bold text-[20px]" />
      </TabGroup>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>