<script setup lang="ts">
import { onMounted, ref } from "vue";
import { getBoundingClientRect } from "@/utils/utils.ts";

const model = defineModel();
defineProps({
  center: Boolean
});

const elRef = ref();

const setXRadius = () => {
  const { height } = getBoundingClientRect(elRef.value);
  elRef.value.style.borderRadius = `${height}px`;
};

onMounted(() => {
  setXRadius();
});
</script>

<template>
  <div ref="elRef" class="bg-[#080808] text-white flex items-center px-10 py-5">
    <img src="/images/icon/decrease.png" alt="decrease" class="w-18 cursor-pointer object-contain" />
    <input v-model="model" :class="{ 'text-center': center}" class="border-none size-full flex-1"/>
    <img src="/images/icon/increase.png" alt="increase" class="w-18 cursor-pointer object-contain" />
  </div>
</template>

<style scoped lang="scss">
input {
  appearance: none; /* 去除浏览器样式外观 */
  background: transparent; /* 去除背景颜色 */
  border: none; /* 删除边框 */
  outline: none; /* 禁用聚焦时的默认轮廓 */
  box-shadow: none; /* 禁用任何阴影 */
  caret-color: currentColor; /* 光标颜色和字体颜色匹配 */
}
</style>