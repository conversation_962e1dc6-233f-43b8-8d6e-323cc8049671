<script setup lang="ts">

</script>

<template>
  <div class="g-loading"></div>
</template>

<style scoped lang="scss">
  @keyframes loader {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .g-loading {
    margin: 6rem auto;
    font-size: 1rem;
    position: relative;
    text-indent: -9999em;
    border-top: .3rem solid rgba(208, 2, 27, .2);
    border-right: .3rem solid rgba(208, 2, 27, .2);
    border-bottom: .3rem solid rgba(208, 2, 27, .2);
    border-left: .3rem solid #d0021b;
    transform: translateZ(0);
    animation: loader 1.1s linear infinite;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
  }
</style>