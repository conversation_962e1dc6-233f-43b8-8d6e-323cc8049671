<script setup lang="ts">
  import { Dialog, DialogPanel, DialogTitle, DialogDescription, TransitionRoot, TransitionChild } from '@headlessui/vue'

  defineProps<{
    width?: string; /** rem */
    title?: string;
  }>()

  const show = defineModel<boolean>('show');

  function setShow(value: boolean) {
    show.value = value
  }
</script>

<template>
  <TransitionRoot appear :show="show" as="template">
    <Dialog class="relative z-50" @close="setShow">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" aria-hidden="true" />
      </TransitionChild>

      <div class="fixed inset-0 flex w-screen py-32 items-start justify-center p-4">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0 -translate-y-32"
          enter-to="opacity-100 translate-y-0"
          leave="duration-200 ease-in"
          leave-from="opacity-100 translate-y-0"
          leave-to="opacity-0 -translate-y-32"
        >
          <DialogPanel :style="{ width: width || '58rem' }" class="max-h-[calc(100%-64px)] rounded-2xl overflow-hidden text-white bg-[#2C2D30]">
            <div class="h-42 flex items-center justify-between p-10 text-white text-lg font-bold">
              <slot name="title">
                <DialogTitle>{{ title }}</DialogTitle>
              </slot>
              <svg
                class="cursor-pointer transition-transform ease-in-out hover:rotate-90"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                @click="setShow(false)"
              >
                <path d="M1.76283 0.707292C2.15337 0.316686 2.78663 0.316685 3.17717 0.707292L13.2898 10.8216C13.6802 11.2122 13.6802 11.8452 13.2898 12.2357L12.2338 13.2918C11.8433 13.6824 11.2101 13.6824 10.8195 13.2918L0.706923 3.17748C0.316481 2.78697 0.31648 2.1539 0.706922 1.76339L1.76283 0.707292Z" fill="#7C868E"/>
                <path d="M13.2931 1.76426C13.6835 2.15477 13.6835 2.78784 13.2931 3.17835L3.18049 13.2927C2.78995 13.6833 2.15669 13.6833 1.76615 13.2927L0.710238 12.2366C0.319796 11.8461 0.319796 11.213 0.710238 10.8225L10.8228 0.708166C11.2134 0.317559 11.8466 0.317559 12.2372 0.708165L13.2931 1.76426Z" fill="#7C868E"/>
              </svg>
            </div>

            <DialogDescription class="bg-[#1b1c1d]">
              <slot />
            </DialogDescription>
          </DialogPanel>
        </TransitionChild>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<style scoped lang="scss">

</style>