<script setup lang="ts">
import { ref } from 'vue';
import { Popover, PopoverButton, PopoverPanel, Switch } from '@headlessui/vue'

import GameLimits from '@/components/modals/GameLimits.vue';
import MyBetHistory from '@/components/modals/MyBetHistory.vue';

const enabled = ref(false);

const showGameLimitsModal = ref(false);
const showMyBetHistoryModal = ref(false);

const handleShowGameLimitsModal = (close: () => void) => {
  showGameLimitsModal.value = true;
  close();
}
const handleShowMyBetHistoryModal = (close: () => void) => {
  showMyBetHistoryModal.value = true;
  close();
}
</script>

<template>
  <div class="flex items-center w-full h-46 bg-[#1B1C1D] px-16">
    <div class="ml-auto flex items-center gap-4 text-[1.6rem] text-[#9AA2A4]">
      <strong class="text-[#32A82E]">10000</strong>
      <span class="mr-16">INR</span>
      <Popover class="relative">
        <PopoverButton>
          <svg class="cursor-pointer" width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.34768 1.45419C0.992178 1.45419 0.705078 1.16019 0.705078 0.80469C0.705078 0.44239 0.985378 0.15529 1.34768 0.15529H10.6446C10.73 0.154678 10.8147 0.171058 10.8938 0.203476C10.9729 0.235895 11.0447 0.283706 11.1051 0.344131C11.1656 0.404555 11.2134 0.476388 11.2458 0.555453C11.2782 0.634518 11.2946 0.719239 11.294 0.80469C11.294 1.16019 11.0069 1.45419 10.6446 1.45419H1.34768ZM1.34768 5.65139C0.992178 5.65139 0.705078 5.35749 0.705078 5.00199C0.705078 4.63969 0.985378 4.35259 1.34768 4.35259H10.6446C10.73 4.35198 10.8147 4.36836 10.8938 4.40078C10.9729 4.43319 11.0447 4.48101 11.1051 4.54143C11.1656 4.60186 11.2134 4.67369 11.2458 4.75275C11.2782 4.83182 11.2946 4.91654 11.294 5.00199C11.294 5.35749 11.0069 5.65139 10.6446 5.65139H1.34768ZM1.34768 9.84869C0.992178 9.84869 0.705078 9.55469 0.705078 9.19929C0.705078 8.83699 0.985378 8.54989 1.34768 8.54989H10.6446C10.73 8.54928 10.8147 8.56566 10.8938 8.59808C10.9729 8.6305 11.0447 8.67831 11.1051 8.73873C11.1656 8.79916 11.2134 8.87099 11.2458 8.95005C11.2782 9.02912 11.2946 9.11384 11.294 9.19929C11.294 9.55469 11.0069 9.84869 10.6446 9.84869H1.34768Z" fill="#767B85"/>
          </svg>
        </PopoverButton>
        <transition
          enter-active-class="transition duration-200 ease-out"
          enter-from-class="translate-y-4 opacity-0"
          enter-to-class="translate-y-0 opacity-100"
          leave-active-class="transition duration-150 ease-in"
          leave-from-class="translate-y-0 opacity-100"
          leave-to-class="translate-y-4 opacity-0"
        >
          <PopoverPanel v-slot="{ close }" class="absolute right-0 z-10 mt-12 w-374">
            <div class="overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5">
              <div class="relative bg-[#2C2D30]">
                <div class="p-12 flex items-center">
                  <div class="size-48 bg-[#D9D9D9] rounded-full"></div>
                  <div class="text-[2rem] text-white font-bold ml-12">d***8</div>
                  <div class="bg-[#252528] ring ring-[#3D3E44] flex items-center px-12 w-140 h-48 ml-auto rounded-[2.4rem]">
                    <svg class="size-24" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="20" cy="20" r="19.1596" stroke="#5E6168" stroke-width="1.68082"/>
                      <circle cx="20.0002" cy="11.6667" r="5.82626" stroke="#5E6168" stroke-width="1.68082"/>
                      <path d="M18.4517 19.1738H21.5483C27.5928 19.174 32.4927 24.0737 32.4927 30.1182V33.3721C32.4927 34.1912 32.1676 34.9774 31.5884 35.5566C31.3204 35.8246 31.005 36.0404 30.6587 36.1943L26.9282 37.8525C24.9899 38.714 22.8921 39.1592 20.771 39.1592H19.229C17.1079 39.1592 15.0101 38.714 13.0718 37.8525L9.34131 36.1943C8.99499 36.0404 8.67963 35.8246 8.41162 35.5566C7.83238 34.9774 7.50732 34.1912 7.50732 33.3721V30.1182C7.50732 24.0736 12.4071 19.1738 18.4517 19.1738Z" stroke="#5E6168" stroke-width="1.68082"/>
                    </svg>
                    <div class="text-[#787C81] ml-12 text-center">Change avatar</div>
                  </div>
                </div>

                <div class="bg-[#1B1C1D] divide-y divide-[#2c2d30]">
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14 5.49994C15.3333 5.33327 18 5.99994 18 9.99994C18 13.9999 15.3333 14.9999 14 14.9999M6.5 6.49991L11 2.49991C11.6667 1.83325 13 0.999913 13 2.99991C13 4.99991 13 13.4999 13 17.4999C13 17.9999 12.6 18.9999 11 18.9999L6.5 13.9999H2.5C2.16667 14.1666 1.5 14.1999 1.5 12.9999C1.5 11.7999 1.5 8.83327 1.5 7.49994C1.5 7.1666 1.7 6.49991 2.5 6.49991C3.3 6.49991 5.5 6.49991 6.5 6.49991Z" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M6.5 7V8.5" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M6.5 14V12.5" stroke="#5E6168" stroke-width="1.6"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Sound</p>
                    </div>
                    <div class="ml-auto">
                      <Switch
                        v-model="enabled"
                        :class="enabled ? 'bg-[#29a91b]' : 'bg-[#1b1c1d]'"
                        class="relative inline-flex w-42 h-20 cursor-pointer rounded-full border-2 border-[#8a8b8b] transition-colors duration-200 ease-in-out"
                      >
                        <span
                          aria-hidden="true"
                          :class="enabled ? 'translate-x-22' : 'translate-x-0'"
                          class="pointer-events-none inline-block size-16 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out"
                        />
                      </Switch>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16.5996 4.42285L11.1465 6.24121L6.19922 7.88965V4.57617L16.5996 1.10938V4.42285Z" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M6.2002 4.5V17.5" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M16.5996 1V14" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M3.5 15.7998C5.11131 15.7998 6.2002 16.8962 6.2002 18C6.2002 19.1038 5.11131 20.2002 3.5 20.2002C1.88869 20.2002 0.799805 19.1038 0.799805 18C0.799805 16.8962 1.88869 15.7998 3.5 15.7998Z" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M13.8994 11.7998C15.5107 11.7998 16.5996 12.8962 16.5996 14C16.5996 15.1038 15.5107 16.2002 13.8994 16.2002C12.2881 16.2002 11.1992 15.1038 11.1992 14C11.1992 12.8962 12.2881 11.7998 13.8994 11.7998Z" stroke="#5E6168" stroke-width="1.6"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Music</p>
                    </div>
                    <div class="ml-auto">
                      <Switch
                        v-model="enabled"
                        :class="enabled ? 'bg-[#29a91b]' : 'bg-[#1b1c1d]'"
                        class="relative inline-flex w-42 h-20 cursor-pointer rounded-full border-2 border-[#8a8b8b] transition-colors duration-200 ease-in-out"
                      >
                        <span
                          aria-hidden="true"
                          :class="enabled ? 'translate-x-22' : 'translate-x-0'"
                          class="pointer-events-none inline-block size-16 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out"
                        />
                      </Switch>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="11" cy="12" r="2.2" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M11 0.799805C11.0008 0.799805 11.0031 0.799586 11.0078 0.800781C11.0128 0.802062 11.0233 0.805416 11.0391 0.813477C11.0719 0.830323 11.1264 0.86742 11.1973 0.942383C11.3439 1.09743 11.5132 1.36509 11.6719 1.76172C11.9873 2.55053 12.2002 3.69516 12.2002 5C12.2002 6.30484 11.9873 7.44947 11.6719 8.23828C11.5132 8.63491 11.3439 8.90257 11.1973 9.05762C11.1264 9.13258 11.0719 9.16968 11.0391 9.18652C11.0233 9.19458 11.0128 9.19794 11.0078 9.19922C11.0031 9.20041 11.0008 9.2002 11 9.2002C10.9992 9.2002 10.9969 9.20041 10.9922 9.19922C10.9872 9.19794 10.9767 9.19458 10.9609 9.18652C10.9281 9.16968 10.8736 9.13258 10.8027 9.05762C10.6561 8.90257 10.4868 8.63491 10.3281 8.23828C10.0127 7.44947 9.7998 6.30484 9.7998 5C9.7998 3.69516 10.0127 2.55053 10.3281 1.76172C10.4868 1.36509 10.6561 1.09743 10.8027 0.942383C10.8736 0.86742 10.9281 0.830323 10.9609 0.813477C10.9767 0.805416 10.9872 0.802062 10.9922 0.800781C10.9969 0.799586 10.9992 0.799805 11 0.799805Z" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M20.1166 16.9243C20.1109 16.9357 20.0751 17.001 19.8965 17.0639C19.7124 17.1286 19.4291 17.1685 19.0459 17.1484C18.2822 17.1082 17.2737 16.8364 16.2266 16.3091C15.1792 15.7817 14.3599 15.133 13.8729 14.5432C13.6286 14.2474 13.4928 13.9964 13.4352 13.81C13.3792 13.6287 13.4106 13.5614 13.4163 13.5501C13.422 13.5387 13.4574 13.4734 13.6364 13.4105C13.8205 13.3458 14.103 13.3055 14.4861 13.3256C15.2499 13.3657 16.2589 13.6378 17.3063 14.1653C18.3533 14.6926 19.1721 15.3411 19.6591 15.9308C19.9034 16.2266 20.04 16.478 20.0977 16.6644C20.1535 16.8454 20.1223 16.913 20.1166 16.9243Z" stroke="#5E6168" stroke-width="1.6"/>
                      <path d="M1.61391 16.9243C1.61785 16.9322 1.65191 16.997 1.83397 17.0595C2.02087 17.1237 2.30774 17.1624 2.69675 17.14C3.47243 17.0953 4.49711 16.8161 5.56144 16.2801C6.62573 15.7442 7.46014 15.0871 7.95789 14.4905C8.2075 14.1913 8.34721 13.9377 8.4069 13.7494C8.46503 13.5659 8.43325 13.4999 8.42931 13.4921C8.42536 13.4843 8.39131 13.4195 8.20924 13.3569C8.02237 13.2927 7.73547 13.254 7.34646 13.2764C6.5708 13.3211 5.54608 13.6003 4.48178 14.1363C3.41745 14.6723 2.58309 15.3293 2.08533 15.9259C1.8357 16.2251 1.69603 16.4787 1.63631 16.667C1.57818 16.8506 1.60996 16.9165 1.61391 16.9243Z" stroke="#5E6168" stroke-width="1.6"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Animation</p>
                    </div>
                    <div class="ml-auto">
                      <Switch
                        v-model="enabled"
                        :class="enabled ? 'bg-[#29a91b]' : 'bg-[#1b1c1d]'"
                        class="relative inline-flex w-42 h-20 cursor-pointer rounded-full border-2 border-[#8a8b8b] transition-colors duration-200 ease-in-out"
                      >
                        <span
                          aria-hidden="true"
                          :class="enabled ? 'translate-x-22' : 'translate-x-0'"
                          class="pointer-events-none inline-block size-16 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out"
                        />
                      </Switch>
                    </div>
                  </div>
                </div>

                <div class="bg-[#1B1C1D] mt-26 divide-y divide-[#2c2d30]">
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 23 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8.5 7.5L11.5 2L14.5 7.5H20.5L15.5 12.5L17 18.5L11.5 15.5L5.5 18.5L7.5 12.5L2.5 7.5H8.5Z" stroke="#5E6168" stroke-width="1.6"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Free Bets</p>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12" @click="handleShowMyBetHistoryModal(close)">
                    <svg class="size-24" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.45041 7.37599e-05C7.94617 -0.00620883 6.4678 0.388962 5.16956 1.14435C3.87133 1.89974 2.80082 2.98766 2.06974 4.29458L0 2.23953V8.0548H5.84965L3.33013 5.54756C4.49905 3.3119 6.75048 1.79114 9.44914 1.79114C13.1401 1.79114 16.1996 4.83139 16.1996 8.50067C16.1996 12.1687 13.1401 15.2115 9.44914 15.2115C6.47985 15.2115 4.04927 13.332 3.0595 10.7376H1.17018C2.15995 14.3159 5.49008 17 9.45041 17C14.2201 17 18 13.1526 18 8.50067C18 3.84871 14.1299 7.37599e-05 9.44914 7.37599e-05H9.45041ZM8.09981 4.4752V9.03874L12.3308 11.5435L13.0499 10.3814L9.44914 8.23416V4.47267H8.09981V4.4752Z" fill="#5E6168"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">My Bet History</p>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12" @click="handleShowGameLimitsModal(close)">
                    <svg class="size-24" viewBox="0 0 19 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="0.8" y="0.8" width="17.4" height="10.4" rx="1.2" stroke="#5E6168" stroke-width="1.6"/>
                      <rect x="0.8" y="0.8" width="17.4" height="10.4" rx="4.2" stroke="#5E6168" stroke-width="1.6"/>
                      <circle cx="10" cy="6" r="2.5" stroke="#5E6168"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Game Limits</p>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9" cy="9" r="9" fill="#3B3D40"/>
                      <path d="M7.42969 7.42188H5.78906C5.77995 6.59245 6.05794 5.9043 6.62305 5.35742C7.17904 4.81055 7.91276 4.53711 8.82422 4.53711C9.40755 4.53711 9.9043 4.65104 10.3145 4.87891C10.7337 5.09766 11.0664 5.42122 11.3125 5.84961C11.5586 6.26888 11.6816 6.71549 11.6816 7.18945C11.6816 7.44466 11.6406 7.69531 11.5586 7.94141C11.4857 8.1875 11.3626 8.43815 11.1895 8.69336C11.0892 8.84831 10.8477 9.10352 10.4648 9.45898C10.1094 9.80534 9.88607 10.0514 9.79492 10.1973C9.70378 10.334 9.6263 10.5208 9.5625 10.7578C9.4987 10.9857 9.45768 11.3092 9.43945 11.7285H7.74414V11.4277C7.74414 10.8535 7.8444 10.3294 8.04492 9.85547C8.25456 9.38151 8.58724 8.9349 9.04297 8.51562C9.48047 8.11458 9.74479 7.84115 9.83594 7.69531C9.92708 7.54036 9.97266 7.35807 9.97266 7.14844C9.97266 6.875 9.85872 6.64258 9.63086 6.45117C9.41211 6.25977 9.11589 6.16406 8.74219 6.16406C8.35938 6.16406 8.04492 6.28255 7.79883 6.51953C7.55273 6.7474 7.42969 7.04818 7.42969 7.42188ZM8.5918 12.7266C8.90169 12.7266 9.16146 12.8314 9.37109 13.041C9.58984 13.2598 9.69922 13.5241 9.69922 13.834C9.69922 14.1439 9.58984 14.4082 9.37109 14.627C9.16146 14.8457 8.90625 14.9551 8.60547 14.9551C8.30469 14.9551 8.04948 14.8457 7.83984 14.627C7.63021 14.4082 7.52539 14.1439 7.52539 13.834C7.52539 13.5241 7.63021 13.2598 7.83984 13.041C8.04948 12.8314 8.30013 12.7266 8.5918 12.7266Z" fill="#ADAFB1"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">How To Play</p>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5.23476 1C3.39542 1 1.89904 2.45981 1.89904 4.25453V14.033H0.00355197V14.5903C0.00303699 14.6004 0.00269366 14.6105 0.00269366 14.6208L0.00355197 16.0537C-0.0304369 16.6371 0.180365 17.2143 0.599046 17.6781C1.05069 18.1788 1.69923 18.4935 2.38844 18.5488V18.5544H12.5165H12.5199C13.8925 18.5544 15.0095 17.4477 15.0095 16.0878V1H5.23476ZM1.48345 16.8946C1.27711 16.6658 1.17205 16.3915 1.18922 16.0877V15.2086H10.0305L10.031 16.0535C10.004 16.5172 10.1317 16.9767 10.3954 17.3786H2.51427C2.1155 17.355 1.74008 17.1791 1.48345 16.8946ZM13.8232 16.0877C13.8232 16.7994 13.2385 17.3787 12.5517 17.3794C12.1493 17.358 11.7699 17.1811 11.5112 16.8946C11.3049 16.6658 11.1998 16.3915 11.2168 16.0877V14.6485C11.2168 14.6383 11.2165 14.6284 11.2161 14.6184V14.033H3.08556V4.25453C3.08556 3.12757 4.0697 2.17554 5.23459 2.17554H13.823L13.8232 16.0877ZM8.08704 4.78712H5.53799C5.21046 4.78712 4.94473 4.52379 4.94473 4.19935C4.94473 3.87491 5.21046 3.61158 5.53799 3.61158H8.08704C8.41457 3.61158 8.6803 3.87491 8.6803 4.19935C8.68013 4.52379 8.41457 4.78712 8.08704 4.78712ZM11.087 7.38316H5.53799C5.21046 7.38316 4.94473 7.11983 4.94473 6.79539C4.94473 6.47095 5.21046 6.20762 5.53799 6.20762H11.087C11.4146 6.20762 11.6803 6.47095 11.6803 6.79539C11.6801 7.11983 11.4146 7.38316 11.087 7.38316ZM11.3022 9.97919H5.53799C5.21046 9.97919 4.94473 9.71586 4.94473 9.39142C4.94473 9.06698 5.21046 8.80365 5.53799 8.80365H11.3022C11.6297 8.80365 11.8954 9.06698 11.8954 9.39142C11.8954 9.71586 11.6297 9.97919 11.3022 9.97919Z" fill="#64676F"/>
                      <path d="M5.5376 12.5H11.0867C11.4142 12.5 11.6797 12.2367 11.6799 11.9122C11.6799 11.5878 11.4142 11.3245 11.0867 11.3245H5.5376C5.21007 11.3245 4.94434 11.5878 4.94434 11.9122C4.94434 12.2367 5.21007 12.5 5.5376 12.5Z" fill="#64676F"/>
                      <path d="M16.4004 1.59961C16.7511 1.59975 16.9812 1.78513 17.1543 2.10059C17.3388 2.43698 17.4004 2.83538 17.4004 3V6C17.4004 6.00616 17.3969 6.04353 17.3721 6.10742C17.3484 6.16819 17.3123 6.233 17.2695 6.29004C17.2261 6.34795 17.1864 6.38347 17.1611 6.40039H14.5996V1.59961H16.4004Z" stroke="#64676F" stroke-width="1.2"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Game Rules</p>
                    </div>
                  </div>
                  <div class="cursor-pointer flex items-center h-50 py-16 pl-16 pr-12">
                    <svg class="size-24" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.9996 20.7968C10.647 20.7966 10.3046 20.6783 10.0272 20.4608L7.98746 18.8653C6.56678 17.7534 5.41783 16.3327 4.62765 14.7109C3.83747 13.0891 3.4268 11.3087 3.42676 9.5046V4.99288C3.42733 4.68787 3.53817 4.39335 3.73882 4.16363C3.93948 3.93391 4.21643 3.78449 4.51859 3.74292C8.45625 3.21569 9.98895 1.87421 10.3649 1.47546C10.5235 1.30635 10.7416 1.20543 10.9732 1.19397C11.2048 1.1825 11.4317 1.2614 11.6063 1.41401C12.1374 1.87593 14.1286 3.40991 17.45 3.75753C17.7594 3.79022 18.0456 3.93664 18.2531 4.16839C18.4607 4.40014 18.5748 4.7007 18.5732 5.01179V9.5046C18.5731 11.3087 18.1623 13.0892 17.372 14.711C16.5816 16.3328 15.4325 17.7535 14.0117 18.8653L11.972 20.4616C11.6944 20.6789 11.352 20.7969 10.9996 20.7968ZM4.80176 5.09085V9.5046C4.79701 11.1008 5.15809 12.6769 5.85729 14.1119C6.55649 15.5469 7.57521 16.8026 8.83523 17.7825L10.875 19.3788C10.9106 19.4068 10.9545 19.422 10.9998 19.422C11.0451 19.422 11.089 19.4068 11.1246 19.3788L13.1639 17.7825C14.4239 16.8026 15.4426 15.5469 16.1418 14.1119C16.841 12.6769 17.2021 11.1008 17.1974 9.5046V5.11319C14.0405 4.75655 11.9449 3.42151 11.0425 2.72628C10.23 3.43226 8.40039 4.59198 4.80176 5.09085Z" fill="#5C6066"/>
                      <path d="M10.5278 13.502H10.5179C10.4261 13.5008 10.3354 13.4811 10.2513 13.4442C10.1671 13.4073 10.0912 13.3539 10.0281 13.2872L7.48304 10.5943C7.35768 10.4618 7.29011 10.2849 7.29519 10.1026C7.30027 9.92019 7.37758 9.74732 7.51011 9.62197C7.64265 9.49661 7.81955 9.42904 8.00191 9.43411C8.18427 9.43919 8.35714 9.5165 8.48249 9.64904L10.5416 11.828L14.3155 8.05404C14.445 7.92819 14.6189 7.85836 14.7995 7.85964C14.9801 7.86092 15.1529 7.9332 15.2806 8.06088C15.4083 8.18855 15.4807 8.36135 15.4821 8.54194C15.4834 8.72253 15.4137 8.89641 15.2879 9.02599L11.0134 13.3005C10.8846 13.4294 10.71 13.5018 10.5278 13.502Z" fill="#5C6066"/>
                    </svg>
                    <div class="ml-12">
                      <p class="text-base font-medium text-white">Provably Fair Settings</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </PopoverPanel>
        </transition>
      </Popover>
    </div>
    <div class="w-3 h-20 bg-[#2D2E2F] ml-16 mr-16"></div>
    <svg class="cursor-pointer" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.86939 13.2832C2.17799 13.2832 1.92599 12.6679 2.29519 12.1757C2.54719 11.8417 2.99829 11.1855 3.25019 10.705C1.41619 9.81444 0.215088 8.09174 0.215088 6.16994C0.215088 3.14644 3.23269 0.714844 7.00029 0.714844C10.7678 0.714844 13.7854 3.14644 13.7854 6.16994C13.7854 9.29884 10.6975 11.6894 6.56669 11.6132C5.33029 12.4921 3.68389 13.2832 2.86939 13.2832ZM3.56669 12.0937C4.09399 11.8828 5.14279 11.2207 5.84599 10.6992C6.08029 10.5175 6.26779 10.4414 6.51979 10.4414C6.73069 10.4472 6.88889 10.4531 7.00029 10.4531C10.1233 10.4531 12.6077 8.53124 12.6077 6.16994C12.6077 3.80864 10.1233 1.88674 7.00029 1.88674C3.87729 1.88674 1.39279 3.80864 1.39279 6.16984C1.39279 7.70494 2.42989 9.04094 4.21709 9.88464C4.59209 10.0604 4.65649 10.3416 4.48069 10.6639C4.26389 11.0682 3.81869 11.6483 3.51969 12.0233C3.47289 12.0819 3.50219 12.123 3.56669 12.0937Z" fill="#A3A3A3"/>
    </svg>
  </div>

  <GameLimits v-model:show="showGameLimitsModal" />
  <MyBetHistory v-model:show="showMyBetHistoryModal" />
</template>

<style lang="scss" scoped>

</style>