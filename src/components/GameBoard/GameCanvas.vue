<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, useTemplateRef, watchEffect } from 'vue';
import { gsap } from 'gsap';
import { useTimer } from '@/composables/useTimer';
import { formatAmount } from '@/utils/utils';
import { random } from 'radash';

type Point = { x: number, y: number }

type LightColor = { r: number, g: number, b: number }

// 背景相关
const BLADE_COUNT = 36;
const BLADE_COLOR = '#000000';
const BLADE_ANGLE = (Math.PI * 2) / BLADE_COUNT;
const BLADE_WIDTH = Math.PI / BLADE_COUNT;

// 航线/路径相关
const ROUTE_PADDING = 20;
const ROUTE_WIDTH = 4;
const ROUTE_COLOR = '#e41536';
const ROUTE_AREA_COLOR = 'rgba(228, 21, 54, 0.5)';
const ROUTE_CONTROL_POINT = { x: 0.45, y: 1 }; // 曲线控制点
const ROUTE_END_POINT_1 = { x: 0.98, y: 0 }; // 飞机终点1（最高点）
const ROUTE_END_POINT_2 = { x: 1, y: 0.38 }; // 飞机终点2（最低点）
const ROUTE_DIED_Y = 0.5; // 飞机坠毁角度

// 飞机相关
const AIRCRAFT_WIDTH = 150;
const AIRCRAFT_HEIGHT = 74;
// 飞机的偏移量，使视觉效果飞机的屁股连接到航线
const AIRCRAFT_OFFSET_X = -20;
const AIRCRAFT_OFFSET_Y = 2;
// 飞机图片
const AIRCRAFT_IMGS = [
  '/images/aircraft-1.png',
  '/images/aircraft-2.png',
  '/images/aircraft-3.png',
  '/images/aircraft-4.png',
]

// 赔率文字相关
const ODDS_FONT_FAMILY = 'Helvetica Neue';
const ODDS_FONT_WEIGHT = 'bold';
const ODDS_FONT_RUN_COLOR = 'white';
const ODDS_FONT_DIED_COLOR = '#cc1420';
const ODDS_LIGHT_OPACITY = 0.6;
const ODDS_LIGHT_COLOR_STEP = [20, 10, 0];
const ODDS_LIGHT_COLORS: LightColor[] = [
  { r: 119, g: 25, b: 111 }, // rgba(119, 25, 111, 0.6)
  { r: 117, g: 54, b: 196 }, // rgba(117, 54, 196, 0.6)
  { r: 61, g: 179, b: 250 }, // rgba(61, 179, 250, 0.6)
]

const containerEl = useTemplateRef('containerEl');
const bgCanvasEl = useTemplateRef('bgCanvasEl');
const fgCanvasEl = useTemplateRef('fgCanvasEl');

let images: HTMLImageElement[] = [];
let bgCtx: CanvasRenderingContext2D | null = null;
let fgCtx: CanvasRenderingContext2D | null = null;
let canvasWidth = 0;
let canvasHeight = 0;
let currentImageIndex = 0;
let rotation = 0;
let moveXT = 0;     // 飞机移动进度
let moveYT = 1;     // 飞机上下飞行进度
let moveDideT = 0;  // 飞机坠毁进度
let moveEndPointFlag = false; // 飞机上下飞行切换标志
let aircraftPosition: Point | null = null; // 飞机当前位置

const { start: startCurrentImageIndex, clean: cleanCurrentImageIndex } = useTimer({ interval: true });
const { start: startOdds, clean: cleanOdds } = useTimer({ interval: true });

// 赔率
const odds = ref(0);

// 发光下标
const lightIdx = ref(0);

// 发光
const light: LightColor = { r: 0, g: 0, b: 0 };

const dideOdds = ref<number>();

const isDide = computed(() => dideOdds.value && odds.value >= dideOdds.value);

// 加载图片
function loadImages() {
  return Promise.all(
    AIRCRAFT_IMGS.map((url) => {
      return new Promise<HTMLImageElement>((resolve, reject) => {
        const img = new Image();
        img.src = url;
        img.onload = () => resolve(img);
        img.onerror = () => reject(`Failed to load image: ${url}`);
      });
    })
  );
}

// 二次贝塞尔曲线
function getQuadraticBezierPoint(t: number, p0: Point, p1: Point, p2: Point) {
  const x = (1-t)*(1-t)*p0.x + 2*(1-t)*t*p1.x + t*t*p2.x;
  const y = (1-t)*(1-t)*p0.y + 2*(1-t)*t*p1.y + t*t*p2.y;
  return { x, y };
}

/**
 * 获取飞行中当前点的位置
 * @param t1 飞机移动进度
 * @param t2 飞机上下飞行进度
 * @param t3 当前绘制进度
 * @param startPoint 开始点位
 * @param controlPoint 贝塞尔曲线控制点
 * @param endPoint1 飞行循环点(最高点)
 * @param endPoint2 飞行循环点(最低点)
 */
function getAircraftPosition(t1: number, t2: number, t3: number, startPoint: Point, controlPoint: Point, endPoint1: Point, endPoint2: Point) {
  let x = endPoint1.x;
  let y = endPoint1.y;

  // 如果已经到达最高点，开始循环上下飞行
  if (t1 >= 1) {
    // 根据当前飞行方向获取当前上下飞行中曲线的结束点
    if (moveEndPointFlag) {
      x = (endPoint2.x - endPoint1.x) * t2 + endPoint1.x;
      y = (endPoint2.y - endPoint1.y) * t2 + endPoint1.y;
    } else {
      x = (endPoint1.x - endPoint2.x) * t2 + endPoint2.x;
      y = (endPoint1.y - endPoint2.y) * t2 + endPoint2.y;
    }
  }

  return getQuadraticBezierPoint(t3, startPoint, controlPoint, { x, y });
}

// 过度发光
function lightTransition(color: LightColor) {
  gsap.to(light, {
    r: color.r,
    g: color.g,
    b: color.b,
    duration: 1,
    ease: 'power2.inOut',
  });
}

// 绘制叶片旋转动画
const renderBgBlade = () => {
  if (bgCanvasEl.value && bgCtx) {
    const radius = Math.sqrt(Math.pow(canvasWidth, 2) + Math.pow(canvasHeight, 2));

    bgCtx.save();
    // 移动到左下角
    bgCtx.translate(0, canvasHeight);
    // 应用旋转
    bgCtx.rotate(rotation);
    // 绘制叶片
    for (let i = 0; i < BLADE_COUNT; i++) {
      const angle = i * BLADE_ANGLE;
      bgCtx.beginPath();
      bgCtx.fillStyle = BLADE_COLOR;
      bgCtx.arc(0, 0, radius, angle - BLADE_WIDTH / 2, angle + BLADE_WIDTH / 2);
      bgCtx.lineTo(0, 0);
      bgCtx.closePath();
      bgCtx.fill();

      // 辅助线
      // bgCtx.beginPath();
      // bgCtx.lineTo(0, 0);
      // bgCtx.lineTo(radius * Math.cos(angle), radius * Math.sin(angle));
      // bgCtx.stroke();
    }
    bgCtx.restore();
  }
}

// 绘制发光
const renderBgLight = () => {
  if (bgCanvasEl.value && bgCtx) {
    bgCtx.save();
    bgCtx.beginPath();
    bgCtx.rect(canvasWidth * 0.3, canvasHeight * 0.2, canvasWidth * 0.4, canvasHeight * 0.6);
    bgCtx.fillStyle = `rgba(${light.r}, ${light.g}, ${light.b}, ${ODDS_LIGHT_OPACITY})`;
    bgCtx.filter = 'blur(120px)';
    bgCtx.closePath();
    bgCtx.fill();
    bgCtx.restore();
  }
}

const renderBg = () => {
  if (bgCanvasEl.value && bgCtx) {
    bgCtx.clearRect(0, 0, canvasWidth, canvasHeight);
    renderBgBlade();

    if (!isDide.value) {
      // 旋转角度
      rotation += 0.003;
      renderBgLight();
    }

    // 更新发光颜色
    for (let i = 0; i < ODDS_LIGHT_COLOR_STEP.length; i++) {
      if (odds.value >= ODDS_LIGHT_COLOR_STEP[i]) {
        if (lightIdx.value !== i) {
          lightIdx.value = i;
          lightTransition(ODDS_LIGHT_COLORS[i]);
        }
        break;
      }
    }
    
    requestAnimationFrame(renderBg);
  }
}

// 绘制航线
const renderFgRoute = (startPoint: Point, controlPoint: Point, endPoint1: Point, endPoint2: Point) => {
  if (fgCanvasEl.value && fgCtx) {
    let positions: Point[] = [];
    
    for (let i = 0; i <= Math.floor(moveXT * 1000); i++) {
      positions.push(getAircraftPosition(moveXT, moveYT, i/1000, startPoint, controlPoint, endPoint1, endPoint2));
    }

    fgCtx.save();

    // 航线路径
    fgCtx.beginPath();
    fgCtx.strokeStyle = ROUTE_COLOR;
    fgCtx.lineWidth = ROUTE_WIDTH;
    // fgCtx.moveTo(startPoint.x, startPoint.y);
    // fgCtx.quadraticCurveTo(controlPoint.x, controlPoint.y, endPoint1.x, endPoint1.y);
    for (let i = 0; i < positions.length; i++) {
      const position = positions[i];
      if (i === 0) {
        fgCtx.moveTo(position.x, position.y);
      } else {
        fgCtx.lineTo(position.x, position.y);
      }
    }
    fgCtx.stroke();
    

    // 航线区域
    fgCtx.beginPath();
    fgCtx.fillStyle = ROUTE_AREA_COLOR;
    // fgCtx.moveTo(startPoint.x, startPoint.y);
    // fgCtx.quadraticCurveTo(controlPoint.x, controlPoint.y, endPoint1.x, endPoint1.y);
    for (let i = 0; i < positions.length; i++) {
      const position = positions[i];
      if (i === 0) {
        fgCtx.moveTo(position.x, position.y);
      } else {
        fgCtx.lineTo(position.x, position.y);
      }
    }
    fgCtx.lineTo(positions[positions.length - 1].x, startPoint.y);
    fgCtx.closePath();
    fgCtx.fill();

    fgCtx.restore();
  }
}

// 绘制飞机
const renderFgAircraft = (point: Point) => {
  if (fgCanvasEl.value && fgCtx) {
    fgCtx.save();

    fgCtx.translate(point.x, point.y);

    if (images.length) {
      fgCtx.drawImage(images[currentImageIndex], AIRCRAFT_OFFSET_X, -AIRCRAFT_HEIGHT + AIRCRAFT_OFFSET_Y, AIRCRAFT_WIDTH, AIRCRAFT_HEIGHT);
    }
    fgCtx.restore();
  }
}

// 绘制飞机飞行
const renderFgAircraftRun = (startPoint: Point, controlPoint: Point, endPoint1: Point, endPoint2: Point) => {
  if (fgCanvasEl.value && fgCtx) {
    aircraftPosition = getAircraftPosition(moveXT, moveYT, moveXT, startPoint, controlPoint, endPoint1, endPoint2);
    renderFgAircraft(aircraftPosition);
  }
}

// 绘制飞机坠毁
const renderFgAircraftDide = (startPoint: Point, endPoint: Point) => {
  if (fgCanvasEl.value && fgCtx) {
    const x = gsap.utils.interpolate(startPoint.x, endPoint.x)(moveDideT);
    const y = gsap.utils.interpolate(startPoint.y, endPoint.y)(moveDideT);
    renderFgAircraft({ x, y });
  }
}

// 绘制赔率文本
const renderFgOddsText = () => {
  if (fgCanvasEl.value && fgCtx) {
    fgCtx.save();

    fgCtx.textAlign = 'center';
    fgCtx.textBaseline = 'middle';

    fgCtx.font = `${ODDS_FONT_WEIGHT} 11.1rem ${ODDS_FONT_FAMILY}`;
    fgCtx.fillStyle = isDide.value ? ODDS_FONT_DIED_COLOR : ODDS_FONT_RUN_COLOR;
    fgCtx.fillText(`${formatAmount(odds.value, 2, true)}x`, canvasWidth / 2, canvasHeight / 2);

    if (isDide.value) {
      fgCtx.font = `${ODDS_FONT_WEIGHT} 4rem ${ODDS_FONT_FAMILY}`;
      fgCtx.fillStyle = 'white';
      fgCtx.fillText('FLEW AWAY!', canvasWidth / 2, canvasHeight / 2 - 120);
    }

    fgCtx.restore();
  }
}

const renderFg = () => {
  if (fgCanvasEl.value && fgCtx) {
    fgCtx.clearRect(0, 0, canvasWidth, canvasHeight);
    const fgPointX = [ROUTE_PADDING, canvasWidth - ROUTE_PADDING];
    const fgPointY = [ROUTE_PADDING, canvasHeight - ROUTE_PADDING];
    const fgWidth = fgPointX[1] - fgPointX[0];
    const fgHeight = fgPointY[1] - fgPointY[0];

    const startPoint: Point = { x: fgPointX[0], y: fgPointY[1] };

    if (isDide.value) {
      const currenPoint = aircraftPosition || startPoint;
      const didePoint = {
        x: canvasWidth + Math.abs(AIRCRAFT_OFFSET_X),
        y: currenPoint.y * ROUTE_DIED_Y,
      };
      const rotate = (canvasWidth - currenPoint.x) / canvasWidth;
      const step = (1 - rotate) / 10;
      // 飞机坠毁移动
      moveDideT = Math.min(moveDideT + step, 1);
      renderFgAircraftDide(currenPoint, didePoint);
    } else {
      const controlPoint = {
        x: fgWidth * ROUTE_CONTROL_POINT.x + fgPointX[0],
        y: fgHeight * ROUTE_CONTROL_POINT.y + fgPointY[0],
      };

      const endPoint1: Point = {
        x: fgWidth * ROUTE_END_POINT_1.x + fgPointX[0] - AIRCRAFT_WIDTH,
        y: fgHeight * ROUTE_END_POINT_1.y + fgPointY[0] + AIRCRAFT_HEIGHT
      };

      const endPoint2: Point = {
        x: canvasWidth * ROUTE_END_POINT_2.x + fgPointX[0] - AIRCRAFT_WIDTH,
        y: canvasHeight * ROUTE_END_POINT_2.y + fgPointY[0] + AIRCRAFT_HEIGHT
      };
      
      // 飞机起飞移动
      moveXT = Math.min(moveXT + 0.004, 1);

      // 达到最高点，开始循环上下飞行
      if (moveXT >= 1) {
        // 切换方向
        if (moveYT >= 1) {
          moveEndPointFlag = !moveEndPointFlag;
          moveYT = 0;
        }
        moveYT = Math.min(moveYT + 0.004, 1);
      }

      renderFgRoute(startPoint, controlPoint, endPoint1, endPoint2);
      renderFgAircraftRun(startPoint, controlPoint, endPoint1, endPoint2);
    }

    renderFgOddsText();
    requestAnimationFrame(renderFg);
  }
}

const resize = () => {
  if (containerEl.value) {
    canvasWidth = containerEl.value.clientWidth;
    canvasHeight = containerEl.value.clientHeight;
  }
  if (bgCanvasEl.value) {
    bgCanvasEl.value.width = canvasWidth;
    bgCanvasEl.value.height = canvasHeight;
  }
  if (fgCanvasEl.value) {
    fgCanvasEl.value.width = canvasWidth;
    fgCanvasEl.value.height = canvasHeight;
  }
}

const start = () => {
  // dideOdds.value = random(2, 12);
  dideOdds.value = 2;
  odds.value = 0;

  rotation = 0;
  moveXT = 0;
  moveYT = 1;
  moveEndPointFlag = false;
  currentImageIndex = 0;

  renderBg();
  renderFg();
  startCurrentImageIndex(() => currentImageIndex = (currentImageIndex + 1) % images.length, 150);
  startOdds(() => odds.value = (odds.value * 1e3 + 0.06 * 1e3) / 1e3, 90);
}

watchEffect(() => {
  if (isDide.value) {
    cleanCurrentImageIndex();
    cleanOdds();
  }
})

onMounted(async () => {
  if (bgCanvasEl.value) {
    bgCtx = bgCanvasEl.value.getContext('2d');
  }
  if (fgCanvasEl.value) {
    fgCtx = fgCanvasEl.value.getContext('2d');
  }

  // 加载图片
  images = await loadImages();

  resize();
  start();
  window.addEventListener('resize', resize);
});

onUnmounted(() => {
  cleanCurrentImageIndex();
  cleanOdds();
  window.removeEventListener('resize', resize);
});
</script>

<template>
  <div ref="containerEl" class="size-full relative">
    <canvas ref="bgCanvasEl" class="absolute inset-0 size-full"></canvas>
    <canvas ref="fgCanvasEl" class="absolute inset-0 size-full"></canvas>
  </div>
</template>

<style scoped lang="scss">

</style>