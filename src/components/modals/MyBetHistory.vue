<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import GModal from '@/components/ui/GModal/index.vue'
  import GLoading from '@/components/ui/GLoading/index.vue';

  const show = defineModel<boolean>('show');

  const loading = ref(true);

  watchEffect(() => {
    if (show.value) {
      loading.value = true;
      setTimeout(() => {
        loading.value = false;
      }, 3000);
    }
  })
</script>

<template>
  <GModal v-model:show="show" title="MY BET HISTORY">
    <div v-if="loading" class="overflow-hidden">
      <GLoading />
    </div>
    <div v-else class="px-16 py-10 select-none">
      <div class="flex mb-6 text-[#797979] text-xs font-medium">
        <div class="flex-1">Date</div>
        <div class="flex-1">Bet USD</div>
        <div class="flex-1 text-center">X</div>
        <div class="flex-1 text-right">Cash out USD</div>
        <div class="w-60 shrink-0 text-right"></div>
      </div>

      <div class="space-y-2">
        <div v-for="i in 12" class="flex items-center h-40 px-6 rounded-full text-sm text-[#BBBFC5] bg-[#101112]">
          <div class="flex-1 text-xs">
            <div>16:27</div>
            <div>18-11-24</div>
          </div>
          <div class="flex-1">8,000.00</div>
          <div class="flex-1 text-center">
            <span class="inline-block py-4 px-6 font-bold rounded-full text-odds-1 bg-black/80">1.40X</span>
          </div>
          <div class="flex-1 text-right">23.30</div>
          <div class="w-60 flex items-center justify-end gap-6 shrink-0">
            <svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15.1457 3.38369C12.657 2.64493 10.1537 1.49462 7.90637 0.0572703C7.84779 0.0197769 7.78098 0 7.71293 0C7.64489 0 7.57809 0.0197769 7.51951 0.0572703C5.20739 1.53592 2.8393 2.62402 0.279933 3.38369C0.199498 3.40756 0.128532 3.45941 0.0779524 3.53124C0.027373 3.60308 -1.42523e-05 3.69093 5.56422e-09 3.78128V7.99296C5.56422e-09 12.3301 1.8469 15.2011 3.39638 16.8456C5.06442 18.6164 7.00745 19.5956 7.71293 19.5956C8.41835 19.5956 10.3614 18.6164 12.0294 16.8456C13.5788 15.2011 15.4256 12.3302 15.4256 7.99304V3.7812C15.4256 3.69087 15.3982 3.60303 15.3476 3.53121C15.297 3.45939 15.2261 3.40756 15.1457 3.38369ZM14.6647 7.99296C14.6647 12.0438 12.942 14.7228 11.4968 16.2568C9.85651 17.998 8.10266 18.7711 7.71293 18.7711C7.32314 18.7711 5.56923 17.998 3.92886 16.2568C2.48373 14.7227 0.761038 12.0438 0.761038 7.99296V4.09414C3.20845 3.34125 5.48774 2.29069 7.71301 0.889821C9.88265 2.25354 12.2756 3.35636 14.6647 4.09391V7.99296Z" fill="url(#paint0_linear_521_5682)"/>
              <path d="M4.34055 4.04806C4.32224 3.99768 4.29495 3.95171 4.26024 3.91277C4.22553 3.87382 4.18407 3.84267 4.13824 3.82108C4.09241 3.7995 4.04311 3.78791 3.99314 3.78697C3.94318 3.78603 3.89354 3.79577 3.84704 3.81562C3.16475 4.10675 2.47311 4.37152 1.77345 4.60943C1.69616 4.63571 1.62862 4.68803 1.58067 4.75876C1.53272 4.82949 1.50686 4.91495 1.50684 5.00271V6.59456C1.50819 6.70292 1.54887 6.80634 1.62008 6.88244C1.69129 6.95855 1.78728 7.00122 1.88731 7.00122C1.98734 7.00122 2.08335 6.95855 2.15456 6.88244C2.22576 6.80634 2.26645 6.70292 2.2678 6.59456V5.30402C2.89396 5.0845 3.51366 4.84392 4.126 4.58263C4.17249 4.5628 4.21493 4.53324 4.25088 4.49564C4.28683 4.45804 4.31559 4.41314 4.33551 4.3635C4.35543 4.31385 4.36613 4.26044 4.36699 4.20632C4.36786 4.1522 4.35888 4.09842 4.34055 4.04806ZM5.06326 4.12629C5.11581 4.12625 5.16778 4.11443 5.21591 4.09155L5.22287 4.08831C5.26858 4.06657 5.30988 4.03529 5.34441 3.99625C5.37895 3.95721 5.40605 3.91118 5.42414 3.86078C5.44224 3.81039 5.45097 3.75662 5.44987 3.70256C5.44877 3.6485 5.43784 3.59521 5.41771 3.54573C5.33336 3.33716 5.10793 3.24284 4.91562 3.33414L4.90948 3.33701C4.86375 3.35867 4.82242 3.38989 4.78783 3.42888C4.75325 3.46787 4.72611 3.51387 4.70796 3.56423C4.6898 3.6146 4.68099 3.66835 4.68204 3.72241C4.68308 3.77647 4.69395 3.82978 4.71403 3.87928C4.74376 3.95278 4.79264 4.01528 4.85467 4.05914C4.9167 4.103 4.98919 4.12638 5.06326 4.12629ZM12.2464 13.2914C12.1619 13.2316 12.0589 13.2106 11.9602 13.2331C11.8614 13.2555 11.7749 13.3195 11.7197 13.411C11.4062 13.9309 11.0384 14.4288 10.6263 14.8906C10.2874 15.2704 9.92254 15.622 9.53491 15.9425C9.45724 16.0098 9.40696 16.1073 9.39481 16.2142C9.38266 16.3211 9.40962 16.429 9.46991 16.5146C9.53021 16.6003 9.61905 16.657 9.71744 16.6726C9.81582 16.6882 9.91594 16.6614 9.99636 16.5981C10.4144 16.2524 10.8078 15.8732 11.1733 15.4636C11.6189 14.9645 12.017 14.4255 12.3568 13.8619C12.3841 13.8166 12.4029 13.7659 12.4122 13.7127C12.4214 13.6596 12.4209 13.6049 12.4107 13.5519C12.4004 13.499 12.3806 13.4487 12.3525 13.404C12.3243 13.3593 12.2882 13.321 12.2464 13.2914ZM8.65126 16.589L8.62943 16.6029C8.54423 16.6588 8.48267 16.7487 8.45796 16.8532C8.43324 16.9578 8.44736 17.0686 8.49727 17.162C8.54717 17.2554 8.62889 17.3239 8.72488 17.3527C8.82087 17.3816 8.92347 17.3685 9.01065 17.3163L9.03582 17.3003C9.12289 17.245 9.18614 17.1546 9.21165 17.0488C9.23716 16.943 9.22284 16.8306 9.17184 16.7363C9.12085 16.642 9.03735 16.5735 8.93972 16.5458C8.84209 16.5182 8.73833 16.5338 8.65126 16.589ZM5.11958 8.95651C5.00496 8.83168 4.86863 8.73271 4.71846 8.66535C4.5683 8.59799 4.4073 8.56356 4.24477 8.56406C4.08223 8.56358 3.92122 8.59802 3.77104 8.66538C3.62086 8.73274 3.4845 8.83169 3.36984 8.95651C2.88748 9.47915 2.88748 10.3296 3.36984 10.8522L5.61657 13.2861C5.85022 13.5392 6.16097 13.6785 6.49137 13.6785C6.82192 13.6785 7.13266 13.5392 7.36631 13.286L12.0565 8.20491C12.5388 7.68213 12.5388 6.83183 12.0565 6.30935C11.9418 6.1845 11.8055 6.08554 11.6553 6.01817C11.5051 5.95081 11.3441 5.91638 11.1815 5.91689C10.8511 5.91689 10.5403 6.0563 10.3067 6.30935L6.49137 10.4426L5.11958 8.95651ZM10.8447 6.89232C10.9346 6.7949 11.0543 6.74129 11.1816 6.74129C11.2442 6.74109 11.3062 6.75434 11.364 6.78027C11.4218 6.80619 11.4743 6.84427 11.5184 6.89232C11.7041 7.09349 11.7041 7.42085 11.5184 7.62202L6.82825 12.7031C6.78411 12.7511 6.73163 12.7892 6.67382 12.8152C6.616 12.8411 6.55401 12.8543 6.49144 12.8541C6.42886 12.8543 6.36686 12.8411 6.30904 12.8152C6.25121 12.7892 6.19871 12.7512 6.15456 12.7031L3.90796 10.2693C3.72219 10.0681 3.7222 9.74066 3.90782 9.53956C3.99774 9.44215 4.11743 9.38853 4.24471 9.38853C4.37199 9.38853 4.4916 9.44215 4.58152 9.53949L6.22238 11.3171C6.29376 11.3943 6.3905 11.4377 6.49137 11.4377C6.59224 11.4377 6.68899 11.3943 6.76036 11.3171L10.8447 6.89232Z" fill="url(#paint1_linear_521_5682)"/>
              <defs>
                <linearGradient id="paint0_linear_521_5682" x1="153.725" y1="0" x2="1507.74" y2="0" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#57B314"/>
                  <stop offset="1" stop-color="#1E9F04"/>
                </linearGradient>
                <linearGradient id="paint1_linear_521_5682" x1="110.251" y1="3.29932" x2="1068.07" y2="3.29932" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#57B314"/>
                  <stop offset="1" stop-color="#1E9F04"/>
                </linearGradient>
              </defs>
            </svg>
            <svg width="19" height="17" viewBox="0 0 19 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.03027 12.4922L7.26465 12.1104H7.71289C8.00488 12.1104 8.28804 12.1363 8.53027 12.1582C8.78399 12.1811 8.99913 12.2002 9.21289 12.2002C11.5487 12.2002 13.6305 11.5147 15.1084 10.4473C16.5873 9.37912 17.4131 7.9734 17.4131 6.5C17.4131 5.0266 16.5873 3.62088 15.1084 2.55273C13.6305 1.48534 11.5487 0.799805 9.21289 0.799805C6.87708 0.799805 4.79532 1.48534 3.31738 2.55273C1.83848 3.62088 1.0127 5.0266 1.0127 6.5C1.0127 8.24199 1.94669 9.7183 3.88184 10.7988L4.37305 11.0732L4.28125 11.6289L4.28027 11.6299V11.6348C4.27979 11.6376 4.27908 11.6412 4.27832 11.6455C4.27681 11.654 4.27414 11.6656 4.27148 11.6797C4.26616 11.7079 4.25844 11.7472 4.24805 11.7959C4.2272 11.8936 4.19572 12.0308 4.15039 12.1982C4.05991 12.5325 3.91418 12.9928 3.69043 13.5049C3.44322 14.0706 3.09079 14.7189 2.59961 15.333C3.75081 15.2236 4.69164 14.7403 5.41797 14.1914C5.95055 13.7889 6.36005 13.3562 6.63574 13.0234C6.77299 12.8578 6.87597 12.7186 6.94238 12.624C6.97527 12.5772 6.99889 12.5412 7.01367 12.5186C7.02092 12.5075 7.02653 12.4995 7.0293 12.4951C7.02988 12.4942 7.0299 12.4928 7.03027 12.4922ZM7.03027 12.4922L7.03125 12.4912V12.4922H7.03027Z" stroke="#5E646B" stroke-width="1.6"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </GModal>
</template>

<style scoped lang="scss">

</style>