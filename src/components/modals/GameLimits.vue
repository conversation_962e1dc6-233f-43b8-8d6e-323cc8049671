<script setup lang="ts">
  import GModal from '@/components/ui/GModal/index.vue'

  const show = defineModel<boolean>('show');
</script>

<template>
  <GModal v-model:show="show" title="GAME LIMITS">
    <div class="px-18 py-35 select-none">
      <div class="border border-[#292A2B] rounded-2xl divide-y divide-[#292A2B] text-base text-white font-medium">
        <div class="py-8 px-12 flex items-center justify-between">
          <div>Minimum bet USD:</div>
          <div class="py-4 px-12 rounded-full bg-[#123407] ring-2 ring-[#427F00]">1.00</div>
        </div>
        <div class="py-8 px-12 flex items-center justify-between">
          <div>Maximum bet USD:</div>
          <div class="py-4 px-12 rounded-full bg-[#123407] ring-2 ring-[#427F00]">100.00</div>
        </div>
        <div class="py-8 px-12 flex items-center justify-between">
          <div>Maximum win for one bet USD:</div>
          <div class="py-4 px-12 rounded-full bg-[#123407] ring-2 ring-[#427F00]">10,000.00</div>
        </div>
      </div>
    </div>
  </GModal>
</template>

<style scoped lang="scss">

</style>