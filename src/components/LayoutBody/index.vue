<script setup lang="ts">
import ResultList from '@/components/widget/ResultList/index.vue';
import AsidePanel from '@/components/CompDesktop/AsidePanel/index.vue';
import GameCanvas from '@/components/GameBoard/index.vue';
import BetPanel from "@/components/widget/BetPanel.vue";
</script>

<template>
  <div class="h-full p-6 bg-(--body-bg-color) lg:grid lg:grid-cols-[37.2rem_minmax(0,1fr)] lg:gap-6">
    <AsidePanel class="hidden lg:block" />
    <main class="h-full grid grid-rows-[auto_24.4rem_auto_auto] lg:grid-rows-[auto_minmax(0,1fr)_auto] gap-10">
      <div class="mt-4 mb-2 h-24 w-full">
        <ResultList />
      </div>
      <div class="rounded-4xl overflow-hidden border border-[#1E1E20]">
        <GameCanvas />
      </div>
      <div class="h-200 grid grid-cols-2 gap-12">
        <BetPanel />
        <BetPanel />
      </div>
      <AsidePanel class="block lg:hidden" />
    </main>
  </div>
</template>

<style lang="scss" scoped>

</style>