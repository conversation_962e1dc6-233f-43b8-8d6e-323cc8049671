<script setup lang="ts">

</script>

<template>
<div>
  <div class="p-10 bg-[#101112]">
    <div class="flex justify-between items-center">
      <div class="flex flex-col items-center ml-22">
        <div class="w-36 h-36 rounded-[50%] bg-white"></div>
        <div class="text-secondary text-xs">
          7***0
        </div>
      </div>
      <div>
        <div>
          <span class="text-secondary text-xs">Bet INR:</span>
          <span class="text-primary text-base">1,700.00</span>
        </div>
        <div>
          <span class="text-secondary text-xs">X INR:</span>
          <span class="text-[#A51D99] text-base">683.76x</span>
        </div>
        <div>
          <span class="text-secondary text-xs">Win INR:</span>
          <span class="text-primary text-base">1,700.00</span>
        </div>
      </div>
      <img src="/images/icon/passed.png" alt="passed" class="h-18 self-start" />
    </div>
  </div>
  <div class="flex justify-between bg-black pl-9 pr-6 py-5">
    <span class="text-sm text-secondary">18 Nov</span>
    <div>
      <span class="text-secondary text-xs">Round:</span>
      <span class="text-primary text-base">1762.7x</span>
    </div>
    <div class="rounded-3xl bg-[#343439] px-8 py-1">
      <img src="/images/icon/forward.png" alt="passed" class="w-20 inline-block" />
      <img src="/images/icon/chat.png" alt="passed" class="w-18 inline-block ml-2" />
    </div>
  </div>
</div>
</template>

<style scoped lang="scss">

</style>