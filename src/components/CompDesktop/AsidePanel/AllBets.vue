<script setup lang="ts">

</script>

<template>
  <div class="flex flex-col size-full overflow-hidden">
    <div class="w-full p-10 rounded-[1.6rem] bg-[#141516]">
      <div class="flex items-center justify-between">
        <div class="flex -space-x-9">
          <img class="inline-block size-28 rounded-full ring-2 ring-white" src="https://images.unsplash.com/photo-1491528323818-fdd1faba62cc?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="" />
          <img class="inline-block size-28 rounded-full ring-2 ring-white" src="https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="" />
          <img class="inline-block size-28 rounded-full ring-2 ring-white" src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2.25&w=256&h=256&q=80" alt="" />
        </div>
        <div class="text-xl font-bold text-white">863,900.00</div>
      </div>
      <div class="flex items-center text-base text-[#797979] font-medium my-6">
        <span class="text-[#cccccc]">191/908</span>
        <span class="ml-6">Bets</span>
        <span class="ml-auto">Total win USD</span>
      </div>
      <div class="h-10 p-2 bg-[#202021] rounded-full">
        <div class="h-full bg-[#457E1F] w-1/2 rounded-full"></div>
      </div>
    </div>

    <div class="flex text-white text-xs font-medium mt-10 mb-12">
      <div class="flex-1">Player</div>
      <div class="flex-1">Bet USD</div>
      <div class="flex-1 text-center">X</div>
      <div class="flex-1 text-right">Win USD</div>
    </div>

    <div class="space-y-5 flex-1 overflow-auto scrollbar-hidden">
      <div
        v-for="i in 30"
        :class="{
          'bg-[#101112]': i % 2 === 0,
          'bg-[#123405]': i % 2 === 1,
        }"
        class="flex items-center text-white text-sm font-medium h-36 p-4 rounded-full"
      >
        <div class="flex-1 flex items-center gap-10">
          <div class="size-28 rounded-full bg-white"></div>
          <div class="text-[#9AA2A4]">P***6</div>
        </div>
        <div class="flex-1">8,000.00</div>
        <div class="flex-1 text-center text-odds-1">1.40x</div>
        <div class="flex-1 text-right pr-3">12,100</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>