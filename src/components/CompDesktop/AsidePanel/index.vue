<script setup lang="ts">
import { <PERSON>b<PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, TabPanel } from '@headlessui/vue'
import AllBets from './AllBets.vue';
import TopBets from './TopBets.vue';
</script>

<template>
  <div class="overflow-hidden">
    <aside class="grid grid-rows-[auto_minmax(0,1fr)] gap-4 h-full bg-[#1B1C1D] rounded-[2.2rem] p-10">
      <TabGroup>
        <div class="h-38 py-5 px-4 bg-[#141516] rounded-[6.6rem]">
          <TabList class="flex text-white text-xs font-bold h-full">
            <Tab v-slot="{ selected }" as="template">
              <div :class="{ 'bg-[#2c2d30]': selected }" class="flex-1 flex items-center justify-center h-full rounded-[2.8rem] cursor-pointer outline-0 hover:text-[#E21A3C]">
                All Bets
              </div>
            </Tab>
            <Tab v-slot="{ selected }" as="template">
              <div :class="{ 'bg-[#2c2d30]': selected }" class="flex-1 flex items-center justify-center h-full rounded-[2.8rem] cursor-pointer outline-0 hover:text-[#E21A3C]">
                Previous
              </div>
            </Tab>
            <Tab v-slot="{ selected }" as="template">
              <div :class="{ 'bg-[#2c2d30]': selected }" class="flex-1 flex items-center justify-center h-full rounded-[2.8rem] cursor-pointer outline-0 hover:text-[#E21A3C]">
                Top
              </div>
            </Tab>
          </TabList>
        </div>
        <TabPanels class="relative">
          <TabPanel class="size-full"> <AllBets /> </TabPanel>
          <TabPanel class="size-full">Content 2</TabPanel>
          <TabPanel><TopBets /></TabPanel>
        </TabPanels>
      </TabGroup>
    </aside>
  </div>
</template>

<style scoped lang="scss">

</style>