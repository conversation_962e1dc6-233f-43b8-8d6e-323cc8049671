<script setup lang="ts">
import { ref } from "vue";

import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue';
import UserBetItem from "./UserBetItem.vue";

const tabs = ref([ 'X', 'Win', 'Rounds' ]);
const conditions = ref([ 'Day', 'Month', 'Year' ]);
const condition = ref('Day');
</script>

<template>
  <TabGroup>
    <div class="absolute inset-0 flex flex-col">
      <div class="p-4 bg-[#141516] rounded-3xl">
        <TabList class="flex">
          <Tab v-for="tab of tabs" v-slot="{ selected }" as="template">
            <div :class="{ 'is-active': selected }" class="tab-item">
              {{ tab }}
            </div>
          </Tab>
        </TabList>
        <div class="flex mt-4">
          <div v-for="item of conditions" :class="{ 'is-active': condition === item }" class="tab-item"
               @click="condition = item">{{ item }}
          </div>
        </div>
      </div>
      <TabPanels class="overflow-auto flex-1 scrollbar-hidden">
        <TabPanel>
          <UserBetItem v-for="n of 20" class="mt-4"/>
        </TabPanel>
        <TabPanel class="size-full">
          <UserBetItem v-for="n of 6" class="mt-4"/>
        </TabPanel>
        <TabPanel class="size-full">cd</TabPanel>
      </TabPanels>
    </div>
  </TabGroup>
</template>

<style scoped lang="scss">
</style>