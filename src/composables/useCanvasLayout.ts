import {onMounted, onUnmounted} from "vue";
import {debounce} from "radash";

type CanvasLayoutConfig = {
  designDraftWidth: number;
  standardSize: number;
  min?: number;
  max?: number;
};

type CanvasLayoutParams = CanvasLayoutConfig | CanvasLayoutConfig[];

/**
 * 动态计算font-size
 * @param designDraftWidth 设计稿宽度
 * @param standardSize 标准尺寸
 * @param min 最小缩放倍数
 * @param max 最大缩放倍数
 */
export const useCanvasLayout = (params: CanvasLayoutParams) => {
  let resizeObserver: ResizeObserver | null = null;

  // 根据设计稿宽度(断点)来倒序排序
  if (Array.isArray(params)) {
    params = params.sort((a, b) => b.designDraftWidth - a.designDraftWidth);
  } else {
    params = [params];
  }

  const root = document.documentElement;
  const calcLayout = () => {
    const vw = window.innerWidth;
    for (let i = 0; i < params.length; i++) {
      const param = params[i];
      if (vw >= param.designDraftWidth || i === params.length - 1) {
        const { designDraftWidth, standardSize, min = 1, max = 2 } = param;
        const minFontSize = standardSize * min; /** 最小设计稿缩放大小 */
        const maxFontSize = standardSize * max; /** 最大为设计稿缩放大小 */
        const fontSize = vw / designDraftWidth * standardSize;
        const limitFontSize = Math.min(maxFontSize, Math.max(minFontSize, fontSize));
        root.style.fontSize = `${limitFontSize}px`;
        root.style.setProperty('--max-container', `${designDraftWidth / standardSize}rem`);
        return;
      }
    }
  }

  const debounceCalcLayout = debounce({ delay: 100 }, calcLayout);

  const setupResizeObserver = () => {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === root) {
          debounceCalcLayout();
        }
      }
    });

    resizeObserver.observe(root);
  }

  onMounted(() => {
    calcLayout();
    setupResizeObserver();
  });

  onUnmounted(() => {
    resizeObserver?.disconnect();
    resizeObserver = null;
  });
}