<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Upward Trending Curve with PixiJS</title>
  <style>
    body {
      padding: 0;
      margin: 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  </style>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pixi.js/7.4.2/pixi.min.js"></script>

  <canvas id="canvas" width="900" height="400"></canvas>

  <script>

    // Initialize PixiJS application
    const app = new PIXI.Application({
      width: 900,
      height: 400,
      backgroundColor: 0xffffff, // White background
      antialias: true // Smooth rendering
    });
    document.body.appendChild(app.view);

    // Create a Graphics object
    const graphics = new PIXI.Graphics();
    
    // 绘制填充区域（无边框）
    graphics.beginFill(0x00ff00, 0.5); // 绿色，半透明
    graphics.moveTo(0, 400); // 起点
    graphics.quadraticCurveTo(600, 400, 900, 100); // 二次贝塞尔曲线
    graphics.lineTo(900, 400); // 连接到右下角
    graphics.lineTo(0, 400); // 连接到左下角
    graphics.closePath(); // 闭合路径
    graphics.endFill();

    // 绘制曲线（仅线条，无填充）
    graphics.lineStyle(2, 0x0000ff, 1); // 2像素宽，蓝色线条
    graphics.moveTo(0, 400); // 起点
    // quadraticCurveTo(cpX, cpY, toX, toY)
    graphics.quadraticCurveTo(600, 400, 900, 100); // 二次贝塞尔曲线

    // Add the graphics to the stage
    app.stage.addChild(graphics);


    /**
     * ================================================================================
     * 
     * ================================================================================
     */


    // 获取 Canvas 和上下文
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    const maxX = 900;
    const minX = 800;
    const maxY = 100;
    const minY = 10;

    const getEndXY = () => {
      return [
        Math.floor(Math.random() * (maxX - minX) + minX),
        Math.floor(Math.random() * (maxY - minY) + minY),
      ];
    }

    let [a, b] = getEndXY()
    let endX = a;
    let endY = b;

    const render = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      if (Math.floor(endX) === a && Math.floor(endY) === b) {
        [a, b] = getEndXY();
        console.log('a', a, 'b', b);
      }

      if (endX > a) endX -= 0.2;
      if (endX < a) endX += 0.2;
      if (endY > b) endY -= 0.2;
      if (endY < b) endY += 0.2;

      // 设置白色背景
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 绘制填充区域（无边框）
      ctx.beginPath();
      ctx.fillStyle = 'rgba(0, 255, 0, 0.5)'; // 绿色，半透明
      ctx.moveTo(0, 400); // 起点
      ctx.quadraticCurveTo(600, 400, endX, endY); // 二次贝塞尔曲线
      ctx.lineTo(endX, 400); // 连接到右下角
      ctx.lineTo(0, 400); // 连接到左下角
      ctx.closePath();
      ctx.fill();

      // 绘制曲线（仅线条）
      ctx.beginPath();
      ctx.strokeStyle = '#0000FF'; // 蓝色线条
      ctx.lineWidth = 2;
      ctx.moveTo(0, 400); // 起点
      ctx.quadraticCurveTo(600, 400, endX, endY); // 二次贝塞尔曲线
      ctx.stroke();
      requestAnimationFrame(render);
    }
    render();
  </script>
</head>
<body>
</body>
</html>